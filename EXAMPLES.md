# Usage Examples

This document provides practical examples of how to use the AI-Powered Screen Agent for various automation tasks.

## Basic Commands

### File and Folder Operations

#### Create a New Folder in VSCode
```
Voice: "Create a new folder named auto_work in VSCode"
Text: create a new folder named "auto_work" in vscode
```

#### Create a New File
```
Voice: "Create a new file called main.py in VSCode"
Text: create new file main.py in vscode
```

#### Open an Application
```
Voice: "Open Visual Studio Code"
Text: open vscode
```

### Email Automation

#### Send a Reply Email
```
Voice: "Send reply <NAME_EMAIL>"
Text: send reply <NAME_EMAIL>
```

#### Compose New Email
```
Voice: "Send <NAME_EMAIL>"
Text: send new <NAME_EMAIL>
```

#### Search Emails
```
Voice: "Search for project updates in Gmail"
Text: search for "project updates" in gmail
```

### Social Media Automation

#### Post to LinkedIn
```
Voice: "Post this image to my LinkedIn account"
Text: post image to linkedin with interesting caption
```

#### Share Content
```
Voice: "Share this article on LinkedIn"
Text: post to linkedin about productivity tips
```

### Web Browsing

#### Search on Google
```
Voice: "Search for Python tutorials"
Text: search for "python machine learning" on google
```

#### Navigate to Website
```
Voice: "Go to GitHub.com"
Text: navigate to github.com
```

#### Click on Elements
```
Voice: "Click on the login button"
Text: click on "Sign In"
```

## Advanced Use Cases

### Development Workflow

#### Complete Project Setup
```
1. "Open VSCode"
2. "Create new folder named my-project"
3. "Create new file package.json"
4. "Open terminal in VSCode"
5. "Type npm init -y"
```

#### Git Operations
```
1. "Open terminal"
2. "Type git status"
3. "Type git add ."
4. "Type git commit -m 'Initial commit'"
```

### Email Management

#### Process Inbox
```
1. "Open Gmail"
2. "Search for unread emails"
3. "Click on first email"
4. "Reply with thank you message"
```

#### Email Organization
```
1. "Search for <NAME_EMAIL>"
2. "Select all emails"
3. "Move to Important folder"
```

### Content Creation

#### LinkedIn Post with Image
```
1. "Open LinkedIn"
2. "Click on Start a post"
3. "Upload image from desktop"
4. "Type: Excited to share my latest project! 🚀"
5. "Click Post"
```

#### Blog Writing Workflow
```
1. "Open notepad"
2. "Type: Blog Post Title: AI in Automation"
3. "Save as blog-draft.txt"
4. "Open browser"
5. "Search for AI automation statistics"
```

## Voice Command Tips

### Best Practices

1. **Speak Clearly**: Use clear pronunciation and normal speaking pace
2. **Be Specific**: Include application names and specific actions
3. **Use Natural Language**: The AI understands conversational commands
4. **Pause Between Commands**: Allow processing time between commands

### Effective Voice Patterns

#### Good Examples:
- "Create a new folder named 'Documents' in VSCode"
- "Send reply <NAME_EMAIL>"
- "Search for 'machine learning' on Google"
- "Click on the submit button"

#### Less Effective:
- "Do something" (too vague)
- "Create folder" (missing details)
- "Email" (incomplete action)

### Voice Command Modifiers

#### Urgency Indicators:
- "Quickly open Chrome"
- "Immediately send email to..."

#### Precision Modifiers:
- "Carefully click on the small button"
- "Slowly type the password"

## Application-Specific Examples

### VSCode Automation

#### Project Setup:
```
1. "Open VSCode"
2. "Create new folder named react-app"
3. "Open terminal"
4. "Type npx create-react-app ."
5. "Type npm start"
```

#### Code Navigation:
```
1. "Search for function definition"
2. "Go to line 50"
3. "Find all references"
4. "Open file explorer"
```

### Gmail Automation

#### Email Triage:
```
1. "Open Gmail"
2. "Search for emails from last week"
3. "Mark as read"
4. "Archive old emails"
```

#### Quick Responses:
```
1. "Reply to latest email"
2. "Type: Thanks for the update!"
3. "Send email"
```

### LinkedIn Automation

#### Networking:
```
1. "Open LinkedIn"
2. "Search for software engineers"
3. "Send connection request"
4. "Add personal note"
```

#### Content Sharing:
```
1. "Post to LinkedIn"
2. "Share article about AI trends"
3. "Add hashtags #AI #Technology"
4. "Publish post"
```

## Error Handling Examples

### When Commands Fail

#### Retry Strategies:
```
If: "Create folder in VSCode" fails
Try: "Open VSCode first, then create new folder"
```

#### Alternative Approaches:
```
If: Voice recognition fails
Try: Type the command manually
Or: Use simpler vocabulary
```

### Troubleshooting Commands

#### Check Application Status:
```
1. "Is VSCode open?"
2. "Switch to Chrome window"
3. "Refresh the page"
```

#### System Commands:
```
1. "Take screenshot"
2. "Show current applications"
3. "Check internet connection"
```

## Productivity Workflows

### Morning Routine:
```
1. "Open Gmail and check new emails"
2. "Reply to urgent messages"
3. "Open calendar and check today's meetings"
4. "Open VSCode and continue yesterday's project"
```

### End of Day:
```
1. "Save all open files"
2. "Commit changes to git"
3. "Close all applications"
4. "Send status update email to team"
```

### Meeting Preparation:
```
1. "Open presentation file"
2. "Start screen recording"
3. "Open meeting notes"
4. "Join video call"
```

## Custom Workflows

### Creating Custom Commands

You can chain multiple commands for complex workflows:

#### Website Testing:
```
1. "Open Chrome"
2. "Navigate to localhost:3000"
3. "Click on login button"
4. "Type test credentials"
5. "Take screenshot"
```

#### Data Analysis:
```
1. "Open Excel file"
2. "Select data range"
3. "Create pivot table"
4. "Export as PDF"
5. "Email to stakeholders"
```

## Tips for Success

1. **Start Simple**: Begin with basic commands and gradually try more complex ones
2. **Practice Common Patterns**: Repeat frequently used commands to improve recognition
3. **Use Consistent Vocabulary**: Stick to terms the AI recognizes well
4. **Monitor Execution**: Watch the screen to ensure commands execute correctly
5. **Have Fallbacks**: Know manual alternatives for critical tasks

## Command Reference Quick Guide

### File Operations:
- `create/make/new + file/folder + name + in + application`
- `open + filename/application`
- `save + filename`

### Text Operations:
- `type/write + text`
- `search/find + term`
- `copy/paste + content`

### Navigation:
- `click + element_name`
- `scroll + up/down`
- `go to + location/url`

### Application Control:
- `open/launch/start + application`
- `close/exit + application`
- `switch to + application`
